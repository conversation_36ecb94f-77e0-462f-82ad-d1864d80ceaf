from flask import Flask, render_template, request, jsonify
import os
import openai
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

app = Flask(__name__)

# Check if OpenAI API key is set
openai_api_key = os.getenv("OPENAI_API_KEY")
if not openai_api_key:
    print("❌ Error: OPENAI_API_KEY not found in environment variables!")
    print("Please create a .env file with your OpenAI API key:")
    print("OPENAI_API_KEY=your-api-key-here")
    exit(1)

# Configure OpenAI
openai.api_key = openai_api_key

# Teacher-like system prompt
TEACHER_PROMPT = """You are a knowledgeable, friendly, and encouraging teacher. Your role is to:

1. **Be Patient and Supportive**: Always encourage learning and never make students feel bad about asking questions
2. **Explain Clearly**: Break down complex concepts into simple, understandable terms
3. **Use Examples**: Provide real-world examples when possible to make concepts relatable
4. **Ask Follow-up Questions**: Engage students by asking clarifying questions to better understand their needs
5. **Be Enthusiastic**: Show genuine excitement about the topics you're teaching
6. **Adapt Your Style**: Adjust your explanation based on the student's level of understanding
7. **Encourage Critical Thinking**: Help students think through problems rather than just giving answers
8. **Be Positive**: Always maintain an encouraging and positive tone

Remember: You're not just answering questions - you're helping someone learn and grow. Make every interaction educational and inspiring!"""

@app.route('/')
def index():
    """Render the main chat interface"""
    return render_template('index.html')

@app.route('/ask', methods=['POST'])
def ask():
    """Handle chat requests and get responses from OpenAI"""
    try:
        # Check if API key is configured
        if not openai_api_key:
            return jsonify({'error': 'OpenAI API key not configured'}), 500
        
        # Get user message from request
        data = request.get_json()
        user_message = data.get('message', '').strip()
        
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        # Create conversation with teacher-like personality
        messages = [
            {"role": "system", "content": TEACHER_PROMPT},
            {"role": "user", "content": user_message}
        ]
        
        # Get response from OpenAI
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=messages,
            max_tokens=1000,
            temperature=0.7
        )
        
        # Extract the assistant's response
        assistant_response = response.choices[0].message.content
        
        # Return response with timestamp
        return jsonify({
            'response': assistant_response,
            'timestamp': datetime.now().strftime('%H:%M'),
            'success': True
        })
        
    except openai.error.AuthenticationError:
        return jsonify({'error': 'Invalid OpenAI API key. Please check your API key in the .env file.'}), 401
    except openai.error.RateLimitError:
        return jsonify({'error': 'Rate limit exceeded. Please try again later.'}), 429
    except openai.error.APIError as e:
        return jsonify({'error': f'OpenAI API error: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'An unexpected error occurred: {str(e)}'}), 500

if __name__ == '__main__':
    print("🎓 Teacher Chatbot Starting...")
    print("📚 A knowledgeable and friendly teacher is ready to help!")
    print("🌐 Open your browser and go to: http://localhost:5000")
    print("💡 API key loaded from .env file")
    print("-" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000) 