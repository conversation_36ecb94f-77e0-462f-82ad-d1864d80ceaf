<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎓 Teacher Chatbot</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="teacher-info">
                    <div class="teacher-avatar">👨‍🏫</div>
                    <div class="teacher-details">
                        <h1>Teacher Chatbot</h1>
                        <p>Your friendly, knowledgeable teacher is here to help!</p>
                    </div>
                </div>
                <div class="status-indicator" id="statusIndicator">
                    <span class="status-dot"></span>
                    <span class="status-text">Ready to help</span>
                </div>
            </div>
        </header>

        <!-- Chat Container -->
        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <!-- Welcome message -->
                <div class="message teacher-message">
                    <div class="message-content">
                        <div class="message-header">
                            <span class="sender">Teacher</span>
                            <span class="timestamp">Now</span>
                        </div>
                        <div class="message-text">
                            Hello! 👋 I'm your friendly teacher chatbot. I'm here to help you learn and answer any questions you might have. Whether it's about math, science, history, programming, or anything else, feel free to ask! What would you like to learn about today?
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Input Area -->
        <div class="input-container">
            <div class="input-wrapper">
                <textarea 
                    id="messageInput" 
                    placeholder="Ask your teacher anything..."
                    rows="1"
                    maxlength="1000"
                ></textarea>
                <button id="sendButton" class="send-button" disabled>
                    <span class="send-icon">📤</span>
                </button>
            </div>
            <div class="input-footer">
                <span class="char-count" id="charCount">0/1000</span>
                <span class="tip">Press Enter to send, Shift+Enter for new line</span>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>Teacher is thinking...</p>
        </div>
    </div>

    <script>
        // DOM Elements
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const statusIndicator = document.getElementById('statusIndicator');
        const charCount = document.getElementById('charCount');

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            
            // Update character count
            const length = this.value.length;
            charCount.textContent = `${length}/1000`;
            
            // Enable/disable send button
            sendButton.disabled = length === 0;
        });

        // Handle Enter key
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Send button click
        sendButton.addEventListener('click', sendMessage);

        // Send message function
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message to chat
            addMessage(message, 'user');
            
            // Clear input
            messageInput.value = '';
            messageInput.style.height = 'auto';
            charCount.textContent = '0/1000';
            sendButton.disabled = true;

            // Show loading
            showLoading();

            try {
                const response = await fetch('/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                if (data.success) {
                    addMessage(data.response, 'teacher', data.timestamp);
                } else {
                    addErrorMessage(data.error || 'An error occurred while getting the response.');
                }
            } catch (error) {
                addErrorMessage('Network error. Please check your connection and try again.');
            } finally {
                hideLoading();
            }
        }

        // Add message to chat
        function addMessage(text, sender, timestamp = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            const time = timestamp || new Date().toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="message-header">
                        <span class="sender">${sender === 'user' ? 'You' : 'Teacher'}</span>
                        <span class="timestamp">${time}</span>
                    </div>
                    <div class="message-text">${formatMessage(text)}</div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        // Add error message
        function addErrorMessage(error) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message error-message';
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="message-text">❌ ${error}</div>
                </div>
            `;
            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        // Format message text (convert markdown-like formatting)
        function formatMessage(text) {
            return text
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/\n/g, '<br>');
        }

        // Scroll to bottom
        function scrollToBottom() {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Show/hide loading
        function showLoading() {
            loadingOverlay.style.display = 'flex';
            statusIndicator.querySelector('.status-text').textContent = 'Thinking...';
            statusIndicator.querySelector('.status-dot').classList.add('thinking');
        }

        function hideLoading() {
            loadingOverlay.style.display = 'none';
            statusIndicator.querySelector('.status-text').textContent = 'Ready to help';
            statusIndicator.querySelector('.status-dot').classList.remove('thinking');
        }

        // Focus input on load
        messageInput.focus();
    </script>
</body>
</html> 