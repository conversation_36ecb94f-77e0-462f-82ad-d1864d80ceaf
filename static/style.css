/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

/* Container */
.container {
    max-width: 1000px;
    margin: 0 auto;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: white;
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
}

/* Header */
.header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 1rem 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.teacher-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.teacher-avatar {
    font-size: 2.5rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.teacher-details h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.teacher-details p {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 300;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    opacity: 0.9;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.status-dot.thinking {
    background: #f59e0b;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Chat Container */
.chat-container {
    flex: 1;
    overflow: hidden;
    background: #f8fafc;
}

.chat-messages {
    height: 100%;
    overflow-y: auto;
    padding: 1.5rem;
    scroll-behavior: smooth;
}

/* Messages */
.message {
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-content {
    max-width: 80%;
    padding: 1rem 1.25rem;
    border-radius: 18px;
    position: relative;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.user-message {
    margin-left: auto;
}

.user-message .message-content {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border-bottom-right-radius: 6px;
}

.teacher-message .message-content {
    background: white;
    border: 1px solid #e2e8f0;
    border-bottom-left-radius: 6px;
}

.error-message .message-content {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    border-bottom-left-radius: 6px;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
    opacity: 0.8;
}

.sender {
    font-weight: 600;
}

.timestamp {
    font-size: 0.75rem;
}

.message-text {
    line-height: 1.6;
    word-wrap: break-word;
}

.message-text strong {
    font-weight: 600;
}

.message-text em {
    font-style: italic;
}

/* Input Area */
.input-container {
    background: white;
    border-top: 1px solid #e2e8f0;
    padding: 1.5rem;
}

.input-wrapper {
    display: flex;
    gap: 0.75rem;
    align-items: flex-end;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 0.75rem;
    transition: border-color 0.3s ease;
}

.input-wrapper:focus-within {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

#messageInput {
    flex: 1;
    border: none;
    background: transparent;
    resize: none;
    font-family: inherit;
    font-size: 0.95rem;
    line-height: 1.5;
    outline: none;
    min-height: 20px;
    max-height: 120px;
}

#messageInput::placeholder {
    color: #94a3b8;
}

.send-button {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
}

.send-button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.send-icon {
    font-size: 1.1rem;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
    font-size: 0.8rem;
    color: #64748b;
}

.char-count {
    font-weight: 500;
}

.tip {
    font-style: italic;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content p {
    color: #64748b;
    font-weight: 500;
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        height: 100vh;
        border-radius: 0;
    }
    
    .header {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .teacher-info {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .teacher-details h1 {
        font-size: 1.25rem;
    }
    
    .chat-messages {
        padding: 1rem;
    }
    
    .message-content {
        max-width: 90%;
    }
    
    .input-container {
        padding: 1rem;
    }
    
    .input-footer {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .message-content {
        max-width: 95%;
        padding: 0.75rem 1rem;
    }
    
    .teacher-avatar {
        width: 50px;
        height: 50px;
        font-size: 2rem;
    }
} 